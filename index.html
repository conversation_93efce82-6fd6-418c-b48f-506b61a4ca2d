<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقديم لوظيفة ويب او تطبيقات فلاتر</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'Tajawal', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header Section -->
    <header class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-lg">ت</span>
                    </div>
                    <h1 class="text-xl font-bold text-gray-800">تقديم لوظيفة</h1>
                </div>
                <nav class="hidden md:flex items-center space-x-6 space-x-reverse">
                    <a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">الرئيسية</a>
                    <a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">الوظائف</a>
                    <a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">حولنا</a>
                    <a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">اتصل بنا</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Job Application Form -->
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <!-- Form Header -->
                <div class="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-8 text-white">
                    <h2 class="text-2xl font-bold mb-2">تقديم لوظيفة مطور ويب أو تطبيقات فلاتر</h2>
                    <p class="text-blue-100">املأ النموذج أدناه لتقديم طلبك للوظيفة</p>
                </div>

                <!-- Form Content -->
                <div class="p-6">
                    <form id="jobApplicationForm" class="space-y-6">
                        <!-- Personal Information Section -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="fullName" class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل *</label>
                                <input type="text" id="fullName" name="fullName" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                                <input type="email" id="email" name="email" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف *</label>
                                <input type="tel" id="phone" name="phone" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label for="city" class="block text-sm font-medium text-gray-700 mb-2">المدينة</label>
                                <input type="text" id="city" name="city"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>

                        <!-- Job Position Section -->
                        <div>
                            <label for="position" class="block text-sm font-medium text-gray-700 mb-2">المنصب المطلوب *</label>
                            <select id="position" name="position" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">اختر المنصب</option>
                                <option value="web-developer">مطور ويب</option>
                                <option value="flutter-developer">مطور تطبيقات فلاتر</option>
                                <option value="fullstack-developer">مطور متكامل</option>
                            </select>
                        </div>

                        <!-- Experience Section -->
                        <div>
                            <label for="experience" class="block text-sm font-medium text-gray-700 mb-2">سنوات الخبرة *</label>
                            <select id="experience" name="experience" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">اختر سنوات الخبرة</option>
                                <option value="0-1">أقل من سنة</option>
                                <option value="1-3">1-3 سنوات</option>
                                <option value="3-5">3-5 سنوات</option>
                                <option value="5+">أكثر من 5 سنوات</option>
                            </select>
                        </div>

                        <!-- Skills Section -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">المهارات التقنية *</label>
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                <label class="flex items-center">
                                    <input type="checkbox" name="skills" value="html" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="mr-2 text-sm">HTML</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="skills" value="css" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="mr-2 text-sm">CSS</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="skills" value="javascript" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="mr-2 text-sm">JavaScript</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="skills" value="typescript" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="mr-2 text-sm">TypeScript</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="skills" value="react" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="mr-2 text-sm">React</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="skills" value="flutter" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="mr-2 text-sm">Flutter</span>
                                </label>
                            </div>
                        </div>

                        <!-- Portfolio Section -->
                        <div>
                            <label for="portfolio" class="block text-sm font-medium text-gray-700 mb-2">رابط المعرض (Portfolio)</label>
                            <input type="url" id="portfolio" name="portfolio"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="https://example.com">
                        </div>

                        <!-- Cover Letter Section -->
                        <div>
                            <label for="coverLetter" class="block text-sm font-medium text-gray-700 mb-2">رسالة تعريفية</label>
                            <textarea id="coverLetter" name="coverLetter" rows="4"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="اكتب رسالة تعريفية موجزة عن نفسك وخبراتك..."></textarea>
                        </div>

                        <!-- File Upload Section -->
                        <div>
                            <label for="resume" class="block text-sm font-medium text-gray-700 mb-2">السيرة الذاتية (PDF) *</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                                <input type="file" id="resume" name="resume" accept=".pdf" required class="hidden">
                                <label for="resume" class="cursor-pointer">
                                    <div class="text-gray-400 mb-2">
                                        <svg class="mx-auto h-12 w-12" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                    </div>
                                    <p class="text-sm text-gray-600">انقر لرفع السيرة الذاتية أو اسحب الملف هنا</p>
                                    <p class="text-xs text-gray-400 mt-1">PDF فقط، حد أقصى 5MB</p>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-center pt-6">
                            <button type="submit"
                                class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                إرسال الطلب
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script src="script.ts"></script>
</body>
</html>